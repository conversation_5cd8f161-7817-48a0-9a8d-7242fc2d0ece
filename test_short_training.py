#!/usr/bin/env python3
"""
短期3个episode的训练测试，验证可微分调节和监督学习
"""

import torch
import numpy as np
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent, OptimalActionProvider
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from config import ENV_CONFIG, AGENT_CONFIG, DEVICE

def test_short_training():
    """3个episode的短期训练测试"""
    print("🚀 开始3个Episode的短期训练测试")
    print("=" * 80)
    
    # 1. 创建环境
    print("1. 初始化环境和智能体...")
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    
    # 2. 创建智能体
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    # 设置环境引用
    agent.set_env(env)
    agent.env_graph_builder = env.graph_builder
    
    # 3. 加载最优动作提供器
    optimal_action_file = os.path.join(os.path.dirname(__file__), 'data', 'power_schedule_with_wind_sequential.csv')
    if os.path.exists(optimal_action_file):
        try:
            optimal_action_provider = OptimalActionProvider(optimal_action_file, env)
            agent.set_optimal_action_provider(optimal_action_provider)
            print("   ✅ 监督学习数据加载成功")
        except Exception as e:
            print(f"   ⚠️ 监督学习数据加载失败: {e}")
    
    print(f"   环境状态维度: {len(state)}")
    print(f"   动作维度: {env.action_space.shape[0]}")
    print(f"   设备: {DEVICE}")
    
    # 训练统计
    episode_rewards = []
    episode_actor_losses = []
    episode_critic_losses = []
    episode_supervision_losses = []
    
    print("\n2. 开始3个Episode的训练...")
    print("=" * 80)
    
    for episode in range(3):
        print(f"\n📍 Episode {episode + 1}/3")
        print("-" * 60)
        
        # 重置环境
        state = env.reset()
        done = False
        episode_steps = 0
        episode_reward = 0.0
        episode_normalized_reward = 0.0
        max_steps = 25  # 增加到25步，确保能触发网络更新
        
        # 开始episode
        agent.start_episode()
        
        step_rewards = []
        step_actions = []
        step_adjusted_actions = []
        
        print(f"   开始时刻: {getattr(env, 'time_step', '未知')}")
        
        while not done and episode_steps < max_steps:
            step_start_time = time.time()
            
            # 选择动作
            action = agent.select_action(state, env.graph_builder, add_noise=True)
            
            # 环境step
            next_state, reward, done, info = env.step(action)
            
            # 记录信息
            step_rewards.append(reward)
            step_actions.append(action.detach().cpu().numpy() if isinstance(action, torch.Tensor) else action)
            
            adjusted_action = info.get('adjusted_action', None)
            if adjusted_action is not None:
                if isinstance(adjusted_action, torch.Tensor):
                    step_adjusted_actions.append(adjusted_action.detach().cpu().numpy())
                else:
                    step_adjusted_actions.append(adjusted_action)
            
            # 转换为图数据
            expected_node_feature_dim = state.shape[0] // agent.time_steps
            graph_list = state_to_graph(
                state, 
                env.graph_builder, 
                time_steps=agent.time_steps,
                node_feature_dim=expected_node_feature_dim
            )
            
            next_graph_list = None
            if not done:
                next_graph_list = state_to_graph(
                    next_state, 
                    env.graph_builder, 
                    time_steps=agent.time_steps,
                    node_feature_dim=expected_node_feature_dim
                )
            
            # 存储transition
            original_reward, normalized_reward = agent.store_transition(
                graph_list, action, reward, next_graph_list, done, info
            )
            
            episode_reward += reward
            episode_normalized_reward += normalized_reward
            
            # 尝试更新网络（如果缓冲区足够）
            actor_loss, critic_loss = 0.0, 0.0
            if len(agent.replay_buffer) >= agent.batch_size:
                actor_loss, critic_loss = agent.update()
            
            step_time = time.time() - step_start_time
            
            # 打印步骤信息
            print(f"     步骤 {episode_steps + 1:2d}: "
                  f"奖励={reward:8.2f}, "
                  f"归一化奖励={normalized_reward:7.4f}, "
                  f"Actor损失={actor_loss:7.4f}, "
                  f"Critic损失={critic_loss:7.4f}, "
                  f"用时={step_time:.3f}s")
            
            state = next_state
            episode_steps += 1
        
        # 结束episode
        agent.end_episode()
        
        # 最终网络更新
        final_actor_loss, final_critic_loss = 0.0, 0.0
        if len(agent.replay_buffer) >= agent.batch_size:
            final_actor_loss, final_critic_loss = agent.update()
        
        # 记录episode统计
        episode_rewards.append(episode_reward)
        episode_actor_losses.append(final_actor_loss)
        episode_critic_losses.append(final_critic_loss)
        
        # 获取监督学习损失
        supervision_loss = 0.0
        if hasattr(agent, 'supervision_loss_history') and len(agent.supervision_loss_history) > 0:
            supervision_loss = agent.supervision_loss_history[-1]
        episode_supervision_losses.append(supervision_loss)
        
        # Episode总结
        print(f"\n   📊 Episode {episode + 1} 总结:")
        print(f"     总步数: {episode_steps}")
        print(f"     总奖励: {episode_reward:.2f}")
        print(f"     归一化总奖励: {episode_normalized_reward:.4f}")
        print(f"     平均奖励: {episode_reward/episode_steps:.2f}")
        print(f"     最终Actor损失: {final_actor_loss:.6f}")
        print(f"     最终Critic损失: {final_critic_loss:.6f}")
        print(f"     监督学习损失: {supervision_loss:.6f}")
        print(f"     缓冲区大小: {len(agent.replay_buffer)}")
        
        # 显示动作统计
        if step_actions:
            actions_array = np.array(step_actions)
            print(f"     动作范围: [{actions_array.min():.3f}, {actions_array.max():.3f}]")
            print(f"     动作均值: {actions_array.mean():.3f}")
        
        if step_adjusted_actions:
            adj_actions_array = np.array(step_adjusted_actions)
            print(f"     调节后动作范围: [{adj_actions_array.min():.1f}, {adj_actions_array.max():.1f}] MW")
            print(f"     调节后动作均值: {adj_actions_array.mean():.1f} MW")
        
        # 显示奖励趋势
        if len(step_rewards) > 1:
            reward_trend = "📈" if step_rewards[-1] > step_rewards[0] else "📉"
            print(f"     奖励趋势: {reward_trend} ({step_rewards[0]:.1f} → {step_rewards[-1]:.1f})")
    
    # 3. 最终统计和分析
    print("\n" + "=" * 80)
    print("📈 3个Episode训练结果分析")
    print("=" * 80)
    
    print(f"\n🎯 整体统计:")
    print(f"   总奖励: {episode_rewards}")
    print(f"   Actor损失: {episode_actor_losses}")
    print(f"   Critic损失: {episode_critic_losses}")
    print(f"   监督损失: {episode_supervision_losses}")
    
    # 趋势分析
    if len(episode_rewards) >= 2:
        reward_improvement = episode_rewards[-1] - episode_rewards[0]
        print(f"\n📊 趋势分析:")
        print(f"   奖励变化: {episode_rewards[0]:.1f} → {episode_rewards[-1]:.1f} (变化: {reward_improvement:+.1f})")
        
        if reward_improvement > 0:
            print("   ✅ 奖励有改善趋势")
        elif reward_improvement < -50:  # 允许小幅波动
            print("   ⚠️ 奖励有下降趋势")
        else:
            print("   ➖ 奖励基本稳定")
    
    # 检查关键功能
    print(f"\n🔧 功能检查:")
    
    # 检查梯度传播
    gradient_working = any(loss > 0 for loss in episode_actor_losses)
    print(f"   梯度传播: {'✅ 正常' if gradient_working else '⚠️ 可能有问题'}")
    
    # 检查监督学习
    supervision_working = any(loss > 0 for loss in episode_supervision_losses)
    print(f"   监督学习: {'✅ 正常' if supervision_working else '⚠️ 未激活或有问题'}")
    
    # 检查网络更新
    network_updating = any(loss > 0 for loss in episode_critic_losses)
    print(f"   网络更新: {'✅ 正常' if network_updating else '⚠️ 缓冲区可能不足'}")
    
    # 最终缓冲区状态
    print(f"   最终缓冲区大小: {len(agent.replay_buffer)}")
    print(f"   需要的最小大小: {agent.batch_size}")
    
    if len(agent.replay_buffer) >= agent.batch_size:
        print("   ✅ 缓冲区大小充足，可以正常训练")
    else:
        print("   ⚠️ 缓冲区大小不足，需要更多经验")
    
    print("\n" + "=" * 80)
    print("🎉 短期训练测试完成！")
    print("=" * 80)
    
    return {
        'episode_rewards': episode_rewards,
        'actor_losses': episode_actor_losses,
        'critic_losses': episode_critic_losses,
        'supervision_losses': episode_supervision_losses,
        'buffer_size': len(agent.replay_buffer),
        'gradient_working': gradient_working,
        'supervision_working': supervision_working,
        'network_updating': network_updating
    }

if __name__ == "__main__":
    print("开始短期训练测试...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        results = test_short_training()
        
        # 简单的成功判断
        success_criteria = [
            results['gradient_working'],
            results['buffer_size'] > 0,
            len(results['episode_rewards']) == 3
        ]
        
        if all(success_criteria):
            print("\n🎉 测试基本成功！主要功能正常工作。")
        else:
            print("\n⚠️ 测试发现一些问题，需要进一步调试。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
