#!/usr/bin/env python3
"""
测试可微分平衡机调节功能
"""

import torch
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from config import ENV_CONFIG, DEVICE

def test_differentiable_adjustment():
    """测试可微分调节功能"""
    print("🔧 测试可微分平衡机调节功能")
    print("=" * 50)
    
    # 1. 创建环境
    print("1. 创建环境...")
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    print(f"   环境创建成功，状态维度: {len(state)}")
    
    # 2. 创建测试动作
    print("\n2. 创建测试动作...")
    action_dim = len(env.controlled_gen_indices)
    test_action = torch.randn(action_dim, requires_grad=True, device=torch.device('cpu'))
    print(f"   测试动作: {test_action.detach().numpy()}")
    print(f"   动作维度: {action_dim}")
    print(f"   requires_grad: {test_action.requires_grad}")
    
    # 3. 测试可微分调节
    print("\n3. 测试可微分调节...")
    try:
        adjusted_action = env.differentiable_adjust_action(test_action)
        print(f"   调节后动作: {adjusted_action.detach().numpy()}")
        print(f"   调节后动作requires_grad: {adjusted_action.requires_grad}")
        
        # 4. 测试梯度传播
        print("\n4. 测试梯度传播...")
        if adjusted_action.requires_grad:
            # 创建一个简单的损失函数
            loss = adjusted_action.sum()
            print(f"   损失值: {loss.item()}")
            
            # 反向传播
            loss.backward()
            print(f"   原始动作梯度: {test_action.grad}")
            print("   ✅ 梯度传播成功！")
        else:
            print("   ❌ 调节后动作没有梯度")
            
    except Exception as e:
        print(f"   ❌ 可微分调节失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 对比原始调节方法
    print("\n5. 对比原始调节方法...")
    try:
        # 使用原始step方法
        next_state, reward, done, info = env.step(test_action.detach().numpy())
        original_adjusted = info.get('adjusted_action', None)
        
        if original_adjusted is not None:
            print(f"   原始方法调节后动作: {original_adjusted}")
            
            # 比较两种方法的结果
            if isinstance(original_adjusted, torch.Tensor):
                diff = torch.abs(adjusted_action.detach() - original_adjusted.detach()).max()
            else:
                diff = np.abs(adjusted_action.detach().numpy() - np.array(original_adjusted)).max()
            print(f"   两种方法的最大差异: {diff}")
            
            if diff < 0.1:  # 允许小的数值误差
                print("   ✅ 两种方法结果基本一致")
            else:
                print("   ⚠️ 两种方法结果存在较大差异")
        else:
            print("   ❌ 原始方法没有返回调节后动作")
            
    except Exception as e:
        print(f"   ❌ 原始方法测试失败: {e}")

def test_batch_adjustment():
    """测试批量调节功能"""
    print("\n" + "=" * 50)
    print("🔧 测试批量可微分调节功能")
    print("=" * 50)
    
    # 创建环境
    env = IEEE30Env(**ENV_CONFIG)
    env.reset()
    
    # 创建批量测试动作
    batch_size = 4
    action_dim = len(env.controlled_gen_indices)
    batch_actions = torch.randn(batch_size, action_dim, requires_grad=True)
    
    print(f"批量动作形状: {batch_actions.shape}")
    print(f"批量动作requires_grad: {batch_actions.requires_grad}")
    
    # 创建模拟的batch_info
    batch_info = {
        'system_states': [
            {'prev_gen_output': None, 'load_demand': None}
            for _ in range(batch_size)
        ]
    }
    
    # 这里我们需要从agent中导入批量调节函数
    # 由于测试环境限制，我们先测试单个样本
    try:
        adjusted_batch = torch.zeros_like(batch_actions)
        for i in range(batch_size):
            adjusted_batch[i] = env.differentiable_adjust_action(batch_actions[i])
        
        print(f"批量调节成功，形状: {adjusted_batch.shape}")
        print(f"批量调节后requires_grad: {adjusted_batch.requires_grad}")
        
        # 测试批量梯度传播
        loss = adjusted_batch.sum()
        loss.backward()
        print(f"批量梯度传播成功，梯度形状: {batch_actions.grad.shape}")
        print("✅ 批量调节和梯度传播都成功！")
        
    except Exception as e:
        print(f"❌ 批量调节失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试可微分平衡机调节功能...")
    
    # 设置随机种子以便复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        test_differentiable_adjustment()
        test_batch_adjustment()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
