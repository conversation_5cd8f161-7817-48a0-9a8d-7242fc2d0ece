#!/usr/bin/env python3
"""
专门测试监督学习功能的调试脚本
"""

import torch
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent, OptimalActionProvider
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from config import ENV_CONFIG, AGENT_CONFIG, DEVICE

def test_supervision_detailed():
    """详细测试监督学习功能"""
    print("🔍 详细测试监督学习功能")
    print("=" * 60)
    
    # 1. 创建环境和智能体
    print("1. 创建环境和智能体...")
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    agent.set_env(env)
    agent.env_graph_builder = env.graph_builder
    
    print(f"   环境创建成功")
    print(f"   智能体创建成功")
    print(f"   动作维度: {env.action_space.shape[0]}")
    
    # 2. 检查监督学习配置
    print(f"\n2. 检查监督学习配置...")
    print(f"   use_supervision: {agent.use_supervision}")
    print(f"   optimal_action_provider: {agent.optimal_action_provider}")
    
    # 3. 尝试加载最优动作提供器
    print(f"\n3. 加载最优动作提供器...")
    optimal_action_file = os.path.join(os.path.dirname(__file__), 'data', 'power_schedule_with_wind_sequential.csv')
    print(f"   最优动作文件路径: {optimal_action_file}")
    print(f"   文件是否存在: {os.path.exists(optimal_action_file)}")
    
    if os.path.exists(optimal_action_file):
        try:
            optimal_action_provider = OptimalActionProvider(optimal_action_file, env)
            agent.set_optimal_action_provider(optimal_action_provider)
            print(f"   ✅ 最优动作提供器加载成功")
            print(f"   数据条数: {len(optimal_action_provider.optimal_data)}")
            print(f"   数据列: {list(optimal_action_provider.optimal_data.columns)}")
        except Exception as e:
            print(f"   ❌ 最优动作提供器加载失败: {e}")
            return False
    else:
        print(f"   ❌ 最优动作文件不存在")
        return False
    
    # 4. 检查监督学习状态
    print(f"\n4. 检查监督学习状态...")
    print(f"   use_supervision: {agent.use_supervision}")
    print(f"   optimal_action_provider: {agent.optimal_action_provider is not None}")
    print(f"   supervision_weight: {agent.get_supervision_weight()}")
    
    # 5. 收集足够的经验来触发网络更新
    print(f"\n5. 收集经验并测试监督学习...")
    print(f"   需要收集至少 {agent.batch_size} 个经验...")

    for i in range(70):  # 收集70个经验，超过64的要求
        action = agent.select_action(state, env.graph_builder, add_noise=False)
        next_state, reward, done, info = env.step(action)
        
        graph_list = state_to_graph(state, env.graph_builder, time_steps=agent.time_steps,
                                   node_feature_dim=state.shape[0] // agent.time_steps)
        next_graph_list = state_to_graph(next_state, env.graph_builder, time_steps=agent.time_steps,
                                        node_feature_dim=next_state.shape[0] // agent.time_steps) if not done else None
        
        agent.store_transition(graph_list, action, reward, next_graph_list, done, info)
        state = next_state
        
        if done:
            state = env.reset()
    
    agent.end_episode()
    
    # 6. 测试网络更新和监督学习
    print(f"\n6. 测试网络更新和监督学习...")
    print(f"   缓冲区大小: {len(agent.replay_buffer)}")
    print(f"   批次大小要求: {agent.batch_size}")
    
    if len(agent.replay_buffer) >= agent.batch_size:
        print(f"   ✅ 缓冲区大小足够，开始更新...")
        
        # 手动检查批次数据
        batch = agent.replay_buffer.sample(agent.batch_size)
        print(f"   批次键: {list(batch.keys())}")
        
        if 'system_states' in batch:
            system_states = batch['system_states']
            print(f"   system_states数量: {len(system_states) if system_states else 0}")
            if system_states and len(system_states) > 0:
                print(f"   第一个system_state类型: {type(system_states[0])}")
                if isinstance(system_states[0], dict):
                    print(f"   第一个system_state键: {list(system_states[0].keys())}")
        else:
            print(f"   ❌ 批次中没有system_states")
        
        # 测试最优动作获取
        if agent.optimal_action_provider and 'system_states' in batch and batch['system_states']:
            try:
                optimal_actions = agent.optimal_action_provider.get_batch_optimal_actions(batch['system_states'])
                if optimal_actions is not None:
                    print(f"   ✅ 成功获取最优动作，形状: {optimal_actions.shape}")
                else:
                    print(f"   ❌ 获取最优动作返回None")
            except Exception as e:
                print(f"   ❌ 获取最优动作失败: {e}")
        
        # 执行更新
        try:
            actor_loss, critic_loss = agent.update()
            print(f"   网络更新成功:")
            print(f"     Actor损失: {actor_loss}")
            print(f"     Critic损失: {critic_loss}")
            
            # 检查监督学习损失历史
            if hasattr(agent, 'supervision_loss_history') and len(agent.supervision_loss_history) > 0:
                recent_supervision_loss = agent.supervision_loss_history[-1]
                print(f"     监督学习损失: {recent_supervision_loss}")
                if recent_supervision_loss > 0:
                    print(f"   ✅ 监督学习正常工作")
                else:
                    print(f"   ⚠️ 监督学习损失为0")
            else:
                print(f"   ⚠️ 没有监督学习损失记录")
                
        except Exception as e:
            print(f"   ❌ 网络更新失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print(f"   ⚠️ 缓冲区大小不足，无法测试网络更新")
    
    print(f"\n" + "=" * 60)
    print(f"🎉 监督学习详细测试完成")
    print(f"=" * 60)
    
    return True

if __name__ == "__main__":
    print("开始监督学习详细测试...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        success = test_supervision_detailed()
        
        if success:
            print("\n✅ 监督学习测试成功")
        else:
            print("\n❌ 监督学习测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
