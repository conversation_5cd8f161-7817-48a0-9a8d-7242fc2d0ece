#!/usr/bin/env python3
"""
测试使用可微分调节的训练过程
"""

import torch
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent, OptimalActionProvider
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from config import ENV_CONFIG, AGENT_CONFIG, DEVICE

def test_training_integration():
    """测试训练集成"""
    print("🚀 测试可微分调节的训练集成")
    print("=" * 60)
    
    # 1. 创建环境
    print("1. 创建环境...")
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    print(f"   环境创建成功，状态维度: {len(state)}")
    
    # 2. 创建智能体
    print("\n2. 创建智能体...")
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    # 设置环境引用
    agent.set_env(env)
    agent.env_graph_builder = env.graph_builder
    
    print(f"   智能体创建成功，动作维度: {env.action_space.shape[0]}")
    
    # 3. 加载最优动作提供器
    print("\n3. 加载最优动作提供器...")
    optimal_action_file = os.path.join(os.path.dirname(__file__), 'data', 'power_schedule_with_wind_sequential.csv')
    if os.path.exists(optimal_action_file):
        try:
            optimal_action_provider = OptimalActionProvider(optimal_action_file, env)
            agent.set_optimal_action_provider(optimal_action_provider)
            print(f"   ✅ 已加载最优动作数据")
        except Exception as e:
            print(f"   ⚠️ 加载最优动作数据失败: {e}")
    else:
        print(f"   ⚠️ 最优动作文件不存在: {optimal_action_file}")
    
    # 4. 测试单步训练
    print("\n4. 测试单步训练...")
    try:
        # 选择动作
        action = agent.select_action(state, env.graph_builder, add_noise=True)
        print(f"   Actor输出动作: {action.detach().cpu().numpy()}")
        print(f"   动作requires_grad: {action.requires_grad}")
        
        # 环境step
        next_state, reward, done, info = env.step(action)
        print(f"   环境step成功，奖励: {reward:.4f}")
        
        # 检查调节后动作
        adjusted_action = info.get('adjusted_action', None)
        if adjusted_action is not None:
            if isinstance(adjusted_action, torch.Tensor):
                print(f"   调节后动作: {adjusted_action.detach().cpu().numpy()}")
                print(f"   调节后动作requires_grad: {adjusted_action.requires_grad}")
            else:
                print(f"   调节后动作: {adjusted_action} (numpy数组)")
        
        # 存储transition
        expected_node_feature_dim = state.shape[0] // agent.time_steps
        graph_list = state_to_graph(
            state, 
            env.graph_builder, 
            time_steps=agent.time_steps,
            node_feature_dim=expected_node_feature_dim
        )
        
        next_graph_list = state_to_graph(
            next_state, 
            env.graph_builder, 
            time_steps=agent.time_steps,
            node_feature_dim=expected_node_feature_dim
        ) if not done else None
        
        original_reward, normalized_reward = agent.store_transition(
            graph_list, action, reward, next_graph_list, done, info
        )
        
        print(f"   存储transition成功")
        print(f"   原始奖励: {original_reward:.4f}, 归一化奖励: {normalized_reward:.4f}")
        
    except Exception as e:
        print(f"   ❌ 单步训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 测试网络更新
    print("\n5. 测试网络更新...")
    
    # 先收集一些经验
    for i in range(10):
        action = agent.select_action(state, env.graph_builder, add_noise=True)
        next_state, reward, done, info = env.step(action)
        
        graph_list = state_to_graph(state, env.graph_builder, time_steps=agent.time_steps,
                                   node_feature_dim=state.shape[0] // agent.time_steps)
        next_graph_list = state_to_graph(next_state, env.graph_builder, time_steps=agent.time_steps,
                                        node_feature_dim=next_state.shape[0] // agent.time_steps) if not done else None
        
        agent.store_transition(graph_list, action, reward, next_graph_list, done, info)
        state = next_state
        
        if done:
            state = env.reset()
    
    # 结束episode
    agent.end_episode()
    
    try:
        # 尝试更新网络
        actor_loss, critic_loss = agent.update()
        print(f"   网络更新成功!")
        print(f"   Actor损失: {actor_loss:.6f}")
        print(f"   Critic损失: {critic_loss:.6f}")
        
        if actor_loss > 0:
            print("   ✅ Actor网络成功更新")
        else:
            print("   ⚠️ Actor网络未更新(可能缓冲区不足)")
            
        if critic_loss > 0:
            print("   ✅ Critic网络成功更新")
        else:
            print("   ⚠️ Critic网络未更新(可能缓冲区不足)")
            
    except Exception as e:
        print(f"   ❌ 网络更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🎉 训练集成测试完成！")
    print("=" * 60)
    
    return True

def test_supervision_learning():
    """测试监督学习功能"""
    print("\n🎓 测试监督学习功能")
    print("=" * 60)
    
    # 创建环境和智能体
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    agent.set_env(env)
    agent.env_graph_builder = env.graph_builder
    
    # 加载最优动作提供器
    optimal_action_file = os.path.join(os.path.dirname(__file__), 'data', 'power_schedule_with_wind_sequential.csv')
    if os.path.exists(optimal_action_file):
        try:
            optimal_action_provider = OptimalActionProvider(optimal_action_file, env)
            agent.set_optimal_action_provider(optimal_action_provider)
            print("✅ 监督学习数据加载成功")
        except Exception as e:
            print(f"❌ 监督学习数据加载失败: {e}")
            return False
    else:
        print("❌ 监督学习数据文件不存在")
        return False
    
    # 收集一些带系统状态的经验
    for i in range(5):
        action = agent.select_action(state, env.graph_builder, add_noise=False)
        next_state, reward, done, info = env.step(action)
        
        graph_list = state_to_graph(state, env.graph_builder, time_steps=agent.time_steps,
                                   node_feature_dim=state.shape[0] // agent.time_steps)
        next_graph_list = state_to_graph(next_state, env.graph_builder, time_steps=agent.time_steps,
                                        node_feature_dim=next_state.shape[0] // agent.time_steps) if not done else None
        
        agent.store_transition(graph_list, action, reward, next_graph_list, done, info)
        state = next_state
        
        if done:
            state = env.reset()
    
    agent.end_episode()
    
    # 测试监督学习更新
    try:
        print("测试监督学习更新...")
        actor_loss, critic_loss = agent.update()
        
        if hasattr(agent, 'supervision_loss_history') and len(agent.supervision_loss_history) > 0:
            supervision_loss = agent.supervision_loss_history[-1]
            print(f"✅ 监督学习损失: {supervision_loss:.6f}")
        else:
            print("⚠️ 未检测到监督学习损失")
        
        print(f"Actor总损失: {actor_loss:.6f}")
        print(f"Critic损失: {critic_loss:.6f}")
        
    except Exception as e:
        print(f"❌ 监督学习测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("🎉 监督学习测试完成！")
    return True

if __name__ == "__main__":
    print("开始测试可微分调节的训练集成...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        success1 = test_training_integration()
        success2 = test_supervision_learning()
        
        if success1 and success2:
            print("\n🎉 所有测试都成功通过！")
            print("✅ 可微分调节功能正常工作")
            print("✅ 梯度传播正常")
            print("✅ 训练集成正常")
            print("✅ 监督学习正常")
        else:
            print("\n⚠️ 部分测试失败，需要进一步调试")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
